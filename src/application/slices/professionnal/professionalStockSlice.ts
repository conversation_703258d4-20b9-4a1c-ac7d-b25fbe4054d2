import { StockDTO } from "@/domain/DTOS/StockDTO.ts";
import { Fournisseurs } from "@/domain/models/Fournisseurs.ts";
import CreateFournisseurUsecase from "@/domain/usecases/fournisseurs/CreateFournisseurUsecase.ts";
import DeleteFournisseurUsecase from "@/domain/usecases/fournisseurs/DeleteFournisseurUsecase.ts";
import GetFournisseursByUserIdUsecase from "@/domain/usecases/fournisseurs/GetFournisseursByUserIdUsecase.ts";
import UpdateFournisseurUsecase from "@/domain/usecases/fournisseurs/UpdateFournisseurUsecase.ts";
import CreateFournisseurRepository from "@/infrastructure/repositories/fournisseurs/CreateFournisseurRepository.ts";
import DeleteFournisseurRepository from "@/infrastructure/repositories/fournisseurs/DeleteFournisseurRepository.ts";
import GetFournisseursByUserIdRepository from "@/infrastructure/repositories/fournisseurs/GetFournisseursByUserIdRepository.ts";
import UpdateFournisseurRepository from "@/infrastructure/repositories/fournisseurs/UpdateFournisseurRepository.ts";
import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";

interface ProfessionalStockSlice {
  stockData: StockDTO[];
  fournisseurs: Fournisseurs[];
  loading: boolean;
  error: string | null;
}

const initialState: ProfessionalStockSlice = {
  stockData: [],
  fournisseurs: [],
  loading: false,
  error: null,
};

export const getStockData = createAsyncThunk(
  "professionalStock/getStockData",
  async (userId: number, { rejectWithValue }) => {
    try {
      console.log("Todo");
      // Todo repository et usecase
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

export const createFournisseur = createAsyncThunk(
  "professionalStock/createFournisseur",
  async (fournisseur: Omit<Fournisseurs, "id">, { rejectWithValue }) => {
    try {
      const createFournisseurRepository = new CreateFournisseurRepository();
      const createFournisseurUsecase = new CreateFournisseurUsecase(
        createFournisseurRepository
      );
      return await createFournisseurUsecase.execute(fournisseur);
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

export const updateFournisseur = createAsyncThunk(
  "professionalStock/updateFournisseur",
  async (
    { id, fournisseur }: { id: number; fournisseur: Omit<Fournisseurs, "id"> },
    { rejectWithValue }
  ) => {
    try {
      const updateFournisseurRepository = new UpdateFournisseurRepository();
      const updateFournisseurUsecase = new UpdateFournisseurUsecase(
        updateFournisseurRepository
      );
      return await updateFournisseurUsecase.execute(id, fournisseur);
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

export const deleteFournisseur = createAsyncThunk(
  "professionalStock/deleteFournisseur",
  async (id: number, { rejectWithValue }) => {
    try {
      const deleteFournisseurRepository = new DeleteFournisseurRepository();
      const deleteFournisseurUsecase = new DeleteFournisseurUsecase(
        deleteFournisseurRepository
      );
      await deleteFournisseurUsecase.execute(id);
      return id;
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

// Recuperation des fournisseurs assiciee a un professionnel
export const getFournisseurs = createAsyncThunk(
  "professionalStock/getFournisseurs",
  async (userId: number, { rejectWithValue }) => {
    try {
      const getFournisseursRepository = new GetFournisseursByUserIdRepository();
      const getFournisseursUsecase = new GetFournisseursByUserIdUsecase(
        getFournisseursRepository
      );
      return await getFournisseursUsecase.execute(userId);
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  }
);

const professionalStockSlice = createSlice({
  name: "professionalStock",
  initialState,
  reducers: {
    setError: (state, action) => {
      state.error = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getStockData.pending, (state) => {
        state.loading = true;
      })
      .addCase(getStockData.fulfilled, (state, action) => {
        state.loading = false;
        state.stockData = action.payload;
      })
      .addCase(getStockData.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as unknown as string;
      })
      // NOTE: Pas besoin de gestion de chargement pour les sous fonctionnalités. Elle est geree dans les hooks respectifs
      .addCase(createFournisseur.fulfilled, (state, action) => {
        state.fournisseurs?.push(action.payload);
      })
      .addCase(createFournisseur.rejected, (state, action) => {
        state.error = action.payload as unknown as string;
      })
      .addCase(updateFournisseur.fulfilled, (state, action) => {
        const index = state.fournisseurs.findIndex(
          (f) => f.id === action.payload.id
        );
        if (index !== -1) {
          state.fournisseurs[index] = action.payload;
        }
      })
      .addCase(updateFournisseur.rejected, (state, action) => {
        state.error = action.payload as unknown as string;
      })
      .addCase(deleteFournisseur.fulfilled, (state, action) => {
        state.fournisseurs = state.fournisseurs.filter(
          (f) => f.id !== action.payload
        );
      })
      .addCase(deleteFournisseur.rejected, (state, action) => {
        state.error = action.payload as unknown as string;
      })
      .addCase(getFournisseurs.fulfilled, (state, action) => {
        state.fournisseurs = action.payload;
      })
      .addCase(getFournisseurs.rejected, (state, action) => {
        state.error = action.payload as unknown as string;
      });
  },
});

export const { setError } = professionalStockSlice.actions;
export default professionalStockSlice.reducer;
