import { useAppSelector } from "../redux.ts";
import useProfessionalStockHandlers from "./use-professional-stock-handlers.ts";

const useProfessionalStock = () => {
  const { utilisateur_id: utilisateurId, id: professionalId } = useAppSelector(
    (state) => state.authentification?.userData
  );

  const handlers = useProfessionalStockHandlers(utilisateurId);

  return {
    handlers,
  };
};

export default useProfessionalStock;
