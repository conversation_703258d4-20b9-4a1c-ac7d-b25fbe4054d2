import { motion } from "framer-motion";
import { Truck } from "lucide-react";

interface FournisseurEmptyTextProps {
  onAddFournisseur: () => void;
  searchTerm: string;
  itemVariants: any;
}

const FournisseurEmptyText: React.FC<FournisseurEmptyTextProps> = ({
  onAddFournisseur,
  searchTerm,
  itemVariants,
}) => {
  return (
    <motion.div
      variants={itemVariants}
      className="bg-white rounded-xl p-12 shadow-sm border border-gray-200 text-center"
    >
      <Truck size={48} className="mx-auto text-gray-300 mb-4" />
      <h3 className="text-lg font-medium text-gray-900 mb-2">
        Aucun fournisseur trouvé
      </h3>
      <p className="text-gray-600 mb-6">
        {searchTerm
          ? "Aucun fournisseur ne correspond à votre recherche."
          : "Commencez par ajouter votre premier fournisseur."}
      </p>
      <motion.button
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        onClick={onAddFournisseur}
        className="bg-gradient-to-r from-meddoc-primary to-meddoc-secondary text-white px-6 py-2 rounded-lg hover:shadow-lg transition-all duration-200"
      >
        Ajouter un fournisseur
      </motion.button>
    </motion.div>
  );
};

export default FournisseurEmptyText;
