import { Fournisseurs } from "@/domain/models/Fournisseurs.ts";
import { motion } from "framer-motion";
import { Truck, Eye, Edit, Trash2 } from "lucide-react";

interface FournisseurCardProps {
  fournisseur: Fournisseurs;
  index: number;
  itemVariants: any;
  cardHoverVariants: any;
  onEditFournisseur: () => void;
  onDeleteFournisseur: () => void;
}

const FournisseurCard: React.FC<FournisseurCardProps> = ({
  fournisseur,
  index,
  itemVariants,
  cardHoverVariants,
  onEditFournisseur,
  onDeleteFournisseur,
}) => {
  return (
    <motion.div
      key={fournisseur.id}
      variants={itemVariants}
      whileHover="hover"
      whileTap="tap"
      custom={index}
      className="bg-white rounded-xl p-6 shadow-sm border border-gray-200 cursor-pointer"
      {...cardHoverVariants}
    >
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-green-100 rounded-lg">
            <Truck size={20} className="text-green-600" />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900 text-sm">
              {fournisseur.nom}
            </h3>
            <p className="text-xs text-gray-500">Fournisseur actif</p>
          </div>
        </div>
        <div className="flex items-center gap-1">
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            className="p-1 text-gray-400 hover:text-meddoc-primary transition-colors"
          >
            <Eye size={16} />
          </motion.button>
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            className="p-1 text-gray-400 hover:text-blue-500 transition-colors"
            onClick={onEditFournisseur}
          >
            <Edit size={16} />
          </motion.button>
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            className="p-1 text-gray-400 hover:text-red-500 transition-colors"
            onClick={onDeleteFournisseur}
          >
            <Trash2 size={16} />
          </motion.button>
        </div>
      </div>

      <div className="space-y-2 text-sm">
        {fournisseur.adresse && (
          <p className="text-gray-600 line-clamp-2">📍 {fournisseur.adresse}</p>
        )}
        {fournisseur.telephone && (
          <p className="text-gray-600">📞 {fournisseur.telephone}</p>
        )}
        {fournisseur.courriel && (
          <p className="text-gray-600">✉️ {fournisseur.courriel}</p>
        )}
      </div>

      <div className="mt-4 pt-4 border-t border-gray-100">
        <div className="flex items-center justify-between text-xs">
          <span className="text-gray-500">Dernière commande</span>
          <span className="text-meddoc-primary font-medium">
            Il y a 3 jours
          </span>
        </div>
      </div>
    </motion.div>
  );
};

export default FournisseurCard;
