import { Fournisseurs } from "@/domain/models/Fournisseurs.ts";
import { motion } from "framer-motion";
import { Plus, Search, Truck, Eye, Edit, Trash2 } from "lucide-react";
import { useEffect, useState } from "react";
import FournisseurCard from "../cards/FournisseurCard.tsx";
import FournisseurEmptyText from "../emptyData/FournisseurEmptyText.tsx";
import FournisseurFormModal from "../modals/FournisseurFormModal.tsx";
import ConfirmationModal from "@/presentation/components/common/Modal/ConfirmationModal.tsx";

/**
 * Composant de section Fournisseurs avec gestion CRUD
 */
interface FournisseursSectionProps {
  fournisseurs: Fournisseurs[];
  onAddFournisseur: (data: Omit<Fournisseurs, "id">) => Promise<boolean>;
  onUpdateFournisseur: (
    id: number,
    data: Omit<Fournisseurs, "id">
  ) => Promise<boolean>;
  onDeleteFournisseur: (id: number) => Promise<boolean>;
  containerVariants: any;
  itemVariants: any;
  cardHoverVariants: any;
  isLoading: boolean;
}

const FournisseursSection: React.FC<FournisseursSectionProps> = ({
  fournisseurs,
  onAddFournisseur,
  onUpdateFournisseur,
  onDeleteFournisseur,
  containerVariants,
  itemVariants,
  cardHoverVariants,
  isLoading,
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredFournisseurs, setFilteredFournisseurs] =
    useState(fournisseurs);

  const [selectedFournisseur, setSelectedFournisseur] =
    useState<Fournisseurs | null>(null);

  // Gestion modal
  const [isAddFournisseurModalOpen, setIsAddFournisseurModalOpen] =
    useState(false);
  const [isEditFournisseurModalOpen, setIsEditFournisseurModalOpen] =
    useState(false);
  const [isDeleteFournisseurModalOpen, setIsDeleteFournisseurModalOpen] =
    useState(false);

  useEffect(() => {
    if (isLoading || !fournisseurs) return;
    const filtered = fournisseurs?.filter(
      (fournisseur) =>
        fournisseur.nom.toLowerCase().includes(searchTerm.toLowerCase()) ||
        fournisseur.courriel?.toLowerCase().includes(searchTerm.toLowerCase())
    );

    setFilteredFournisseurs(filtered);
  }, [fournisseurs, searchTerm, isLoading]);

  const handleStartEditFournisseur = (fournisseurId: number) => {
    setIsEditFournisseurModalOpen(true);
    setSelectedFournisseur(fournisseurs.find((f) => f.id === fournisseurId));
  };

  const handleStartDeleteFournisseur = (fournisseurId: number) => {
    setIsDeleteFournisseurModalOpen(true);
    setSelectedFournisseur(fournisseurs.find((f) => f.id === fournisseurId));
  };

  const handleCancelDeleteFournisseur = () => {
    setIsDeleteFournisseurModalOpen(false);
    setSelectedFournisseur(null);
  };

  const handleApplyEditFournisseur = async (data: Omit<Fournisseurs, "id">) => {
    if (!selectedFournisseur) return;
    const ok = await onUpdateFournisseur(selectedFournisseur.id, data);

    if (ok) {
      setIsEditFournisseurModalOpen(false);
      setSelectedFournisseur(null);
    }
    return ok;
  };

  const handleDeleteFournisseur = async () => {
    if (!selectedFournisseur) return;
    const ok = await onDeleteFournisseur(selectedFournisseur.id);

    if (ok) {
      setIsDeleteFournisseurModalOpen(false);
      setSelectedFournisseur(null);
    }
    return ok;
  };

  return (
    <>
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        exit="hidden"
        className="space-y-6"
      >
        {/* Header avec recherche et bouton d'ajout */}
        <motion.div
          variants={itemVariants}
          className="bg-white rounded-xl p-6 shadow-sm border border-gray-200"
        >
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                Gestion des Fournisseurs
              </h2>
              <p className="text-gray-600 text-sm">
                Gérez vos fournisseurs et leurs informations de contact
              </p>
            </div>
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => setIsAddFournisseurModalOpen(true)}
              className="flex items-center gap-2 bg-gradient-to-r from-meddoc-primary to-meddoc-secondary text-white px-4 py-2 rounded-lg hover:shadow-lg transition-all duration-200"
            >
              <Plus size={18} />
              <span className="font-medium">Nouveau Fournisseur</span>
            </motion.button>
          </div>

          {/* Barre de recherche */}
          <div className="mt-4 relative">
            <Search
              size={18}
              className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
            />
            <input
              type="text"
              placeholder="Rechercher un fournisseur..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-meddoc-primary focus:border-transparent"
            />
          </div>
        </motion.div>

        {/* Liste des fournisseurs */}
        <div className="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 gap-6">
          {filteredFournisseurs.map((fournisseur, index) => (
            <FournisseurCard
              key={fournisseur.id}
              fournisseur={fournisseur}
              index={index}
              itemVariants={itemVariants}
              cardHoverVariants={cardHoverVariants}
              onEditFournisseur={() =>
                handleStartEditFournisseur(fournisseur.id)
              }
              onDeleteFournisseur={() =>
                handleStartDeleteFournisseur(fournisseur.id)
              }
            />
          ))}
        </div>

        {/* Message si aucun fournisseur trouvé */}
        {filteredFournisseurs.length === 0 && (
          <FournisseurEmptyText
            onAddFournisseur={() => setIsAddFournisseurModalOpen(true)}
            searchTerm={searchTerm}
            itemVariants={itemVariants}
          />
        )}
      </motion.div>

      {/* NOTE: Ne pas monter directement les modals que lorsque les donnes de l'etat sont prêtes pour eviter les donnees de modification manquantes par exemple */}
      {/* Modal d'ajout de fournisseur */}
      {isAddFournisseurModalOpen && (
        <FournisseurFormModal
          isOpen={isAddFournisseurModalOpen}
          onClose={() => setIsAddFournisseurModalOpen(false)}
          onSubmit={onAddFournisseur}
          loading={isLoading}
          editionData={undefined}
        />
      )}

      {/* Modal de modification de fournisseur */}
      {isEditFournisseurModalOpen && (
        <FournisseurFormModal
          title="Modifier le fournisseur"
          description="Modifier les informations du fournisseur"
          isOpen={isEditFournisseurModalOpen}
          onClose={() => setIsEditFournisseurModalOpen(false)}
          onSubmit={handleApplyEditFournisseur}
          loading={isLoading}
          editionData={selectedFournisseur}
        />
      )}

      {/* Modal de suppression de fournisseur */}
      {isDeleteFournisseurModalOpen && (
        <ConfirmationModal
          title="Supprimer le fournisseur"
          message="Êtes-vous sûr de vouloir supprimer ce fournisseur ? Cette action est irréversible."
          confirmButtonText={isLoading ? "Suppression..." : "Supprimer"}
          cancelButtonText="Annuler"
          loading={isLoading}
          isOpen={isDeleteFournisseurModalOpen}
          onClose={handleCancelDeleteFournisseur}
          onConfirm={handleDeleteFournisseur}
        />
      )}
    </>
  );
};

export default FournisseursSection;
