import { Categories } from "../models/Categories.ts";
import { EntreesStocks } from "../models/EntreesStocks.ts";
import { Fournisseurs } from "../models/Fournisseurs.ts";
import { Lots } from "../models/Lots.ts";
import { SortiesStocks } from "../models/SortiesStocks.ts";
import { Stocks } from "../models/Stocks.ts";

export interface CreateStockDTO {
  id: number;
  stock: Stocks;
  fournisseur: Fournisseurs;
  quantite: number;
  prix_unitaire: number;
  date_entree: string;
  lot: Lots;
}

// DTO de l'ensemble de donnee a la recuperation global
export interface StockDTO extends Stocks {
  fournisseurs: Fournisseurs[];
  categories: Categories[];
  lots: Lots[];
  entrees: EntreesStocks[];
  sorties: SortiesStocks[];
}
